"use client";

import { <PERSON>, <PERSON>Off, X } from "lucide-react";

import { useState } from "react";

export default function Login() {
  const [showPassword, setShowPassword] = useState(false);
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 gap-[10px] px-4">
      <div className="bg-white rounded-lg overflow-hidden shadow-lg w-full max-w-sm gap-[13px] sm:max-w-md">
        <div className="flex justify-between items-center gap-5 py-4 px-6 bg-[#F2F2F2]">
          <h2 className="text-lg font-semibold"><PERSON><PERSON><PERSON></h2>
          <button title="close" className="text-gray-500 hover:text-black">
            <X size={20} />
          </button>
        </div>
        <div className="gap-4 py-3 px-4">
          <div className="flex flex-col gap-2 mb-4">
            <label htmlFor="username" className="font-semibold">
              T<PERSON><PERSON> t<PERSON><PERSON> k<PERSON><PERSON> đ<PERSON> admin cấp
            </label>
            <input
              type="text"
              name="username"
              id="username"
              className="border px-3 py-2 rounded-md"
            />
          </div>
          <div className="flex flex-col gap-2">
            <label className="block font-semibold">Mật khẩu</label>
            <div className="relative">
              <input
                placeholder="********"
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                className="w-full border rounded-sm px-3 py-2 text-sm pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            <div className=" text-gray-800 text-sm py-3">
              <p>Lấy lại mật khẩu điền dàn liên hệ Facebook</p>
              <span>Admin: http://www.facebook.com/nhom247</span>
            </div>

            <div className="flex items-center gap-x-2 mb-3">
              <input type="checkbox" id="remember" className="h-4 w-4" />
              <label htmlFor="remember" className="font-semibold">
                Duy trì đăng nhập
              </label>
            </div>
          </div>
          <button className=" bg-[#FF0000] text-white px-2 py-[6px] rounded-lg text-sm">
            Đăng nhập
          </button>
        </div>
      </div>
    </div>
  );
}
