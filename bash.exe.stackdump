Stack trace:
Frame         Function      Args
0007FFFF8EA0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7DA0) msys-2.0.dll+0x2118E
0007FFFF8EA0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9178) msys-2.0.dll+0x69BA
0007FFFF8EA0  0002100469F2 (00021028DF99, 0007FFFF8D58, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8EA0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8EA0  00021006A545 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9180  00021006B9A5 (0007FFFF8EB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF464C0000 ntdll.dll
7FFF456B0000 KERNEL32.DLL
7FFF43C30000 KERNELBASE.dll
7FFF445A0000 USER32.dll
7FFF440D0000 win32u.dll
7FFF44DB0000 GDI32.dll
7FFF438C0000 gdi32full.dll
7FFF43B80000 msvcp_win.dll
7FFF435F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF44770000 advapi32.dll
7FFF44DF0000 msvcrt.dll
7FFF44830000 sechost.dll
7FFF46150000 RPCRT4.dll
7FFF42BE0000 CRYPTBASE.DLL
7FFF44030000 bcryptPrimitives.dll
7FFF44D70000 IMM32.DLL
