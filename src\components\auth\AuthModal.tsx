"use client";

import LoginForm from "./LoginForm";
import RegisterForm from "./RegisterForm";
import { X } from "lucide-react";
import { useState } from "react";

interface AuthModalProps {
  onClose: () => void;
  initialMode?: "login" | "register";
}

export default function AuthModal({
  onClose,
  initialMode = "login",
}: AuthModalProps) {
  const [mode, setMode] = useState<"login" | "register">(initialMode);

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 gap-[10px] px-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg overflow-hidden shadow-lg w-full max-w-sm gap-[13px] sm:max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center gap-5 py-4 px-6 bg-[#F2F2F2]">
          <h2 className="text-lg font-semibold">
            {mode === "login" ? "Đăng Nhập" : "Đăng <PERSON>"}
          </h2>
          <button
            title="close"
            className="text-gray-500 hover:text-black"
            onClick={onClose}
          >
            <X size={20} />
          </button>
        </div>

        <div className="gap-4 py-3 px-4">
          {mode === "login" ? (
            <LoginForm onClose={onClose} />
          ) : (
            <RegisterForm onClose={onClose} />
          )}

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              {mode === "login" ? "Chưa có tài khoản?" : "Đã có tài khoản?"}
              <button
                type="button"
                className="ml-1 text-[#FF0000] font-semibold hover:underline"
                onClick={() => setMode(mode === "login" ? "register" : "login")}
              >
                {mode === "login" ? "Đăng ký ngay" : "Đăng nhập"}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
