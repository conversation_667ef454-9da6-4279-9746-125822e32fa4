"use client";

import Image from "next/image";

export default function Footer() {
  return (
    <footer className="bg-[#131313] px-4 py-10 md:p-20 text-white">
      <div className="flex gap-2 items-center">
        <Image src="/assets/logo.png" width={40} height={40} alt="logo" />
        <p className="text-xl text-white whitespace-nowrap">GiaiMaSoHoc Live</p>
      </div>
      <div className="flex flex-wrap justify-between gap-10 mt-[30px] md:gap-[300px]">
        <div>
          <div className="flex gap-2 items-center mb-[20px]">
            <span className="bg-[#FF3130] px-[2.5px] py-[12px]"></span>
            <p className="text-lg">Giới thiệu</p>
          </div>
          <div className="md:w-[385px]">
            <p>
              Diễn đàn CAO THỦ CHỐT SỐ đầu tiên tại VIỆT NAM. TẬP ĐOÀN GIẢI MÃ
              SỐ HỌC | CAO THỦ HỘI TỤ - ĐỈNH CAO CHỐT SỔ
            </p>
          </div>
          <div className="flex flex-warp gap-[15px] pt-5 mt-15 items-center relative">
            <Image
              src="/assets/5be000d23648c3edd857313cbad773562fefe978.png"
              width={92}
              height={32}
              alt="kudv"
            />
            <p>Nhà tài trợ KUBET - KU CASINO</p>
            <div className="border-t border-[#A6A6A6] absolute top-0 left-0 w-[360px]"></div>
          </div>
        </div>
        <div className="flex-1">
          <div className="flex gap-2 items-center mb-[20px]">
            <span className="bg-[#FF3130] px-[2.5px] py-[12px]"></span>
            <p className="text-lg">Diễn đàn xổ số - Cao thủ soi cầu</p>
          </div>
          <div>
            <ul className="flex flex-col gap-1 [&_li]:underline underline-offset-2 ">
              <li>Xổ số miền bắc</li>
              <li>Xổ số miền trung</li>
              <li>Xổ số miền nam</li>
              <li>Sự kiện - Events</li>
              <li>KUBET - KU CASINO</li>
            </ul>
          </div>
          <div className="flex gap-10 items-center mt-5">
            <div className="flex gap-2 items-center">
              <span className="bg-[#FF3130] px-[2.5px] py-[12px]"></span>
              <p className="text-lg">Liên hệ:</p>
            </div>
            <div>
              <ul className="flex flex-row gap-5 items-center">
                <li>
                  <a href="#" title="Youtube">
                    <Image
                      src="/assets/youtube.png"
                      width={32}
                      height={32}
                      alt="youtube"
                      className="object-cover scale-90"
                    />
                  </a>
                </li>
                <li>
                  <a href="#" title="Facebook">
                    <Image
                      src="/assets/facebook.png"
                      width={32}
                      height={32}
                      alt="facebook"
                      className="object-cover scale-90"
                    />
                  </a>
                </li>
                <li>
                  <a href="#" title="Telegram">
                    <Image
                      src="/assets/telegram.png"
                      width={32}
                      height={32}
                      alt="telegram"
                      className="object-cover scale-90"
                    />
                  </a>
                </li>
                <li>
                  <a href="#" title="Tiktok">
                    <Image
                      src="/assets/tiktok.png"
                      width={32}
                      height={32}
                      alt="tiktok"
                      className="object-cover scale-90"
                    />
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
