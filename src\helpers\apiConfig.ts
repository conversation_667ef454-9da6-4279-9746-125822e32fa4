import axios from "axios";

export const BASE_URL = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "https://quantri.giaimasohoc.org/api/v1",
  headers: { "Content-Type": "application/json" },
});

BASE_URL.interceptors.request.use((config) => {
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
  }
  return config;
});

