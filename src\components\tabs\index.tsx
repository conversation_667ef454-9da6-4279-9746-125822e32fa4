import Image from "next/image";
import { useState } from "react";

const tabs = ["Tất cả", "<PERSON><PERSON><PERSON> thích", "Nhóm"];
const listMessage = [
  {
    name: "<PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
    unread: 2,
  },
  {
    name: "<PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:40 AM",
    unread: 2,
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
  },
  {
    name: "<PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:00 AM",
    unread: 2,
  },
  {
    name: "<PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
    unread: 2,
  },
  {
    name: "<PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "09:00 AM",
  },
  {
    name: "<PERSON><PERSON>",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
    unread: 2,
  },
  {
    name: "Tán Gấu",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
  },
  {
    name: "Tán Gấu",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
    unread: 2,
  },
  {
    name: "Minh Long",
    image: "/assets/mc-image.png",
    message: "Hey, how is it going?",
    time: "10:30 AM",
    unread: 2,
  },
];

export default function Tabs() {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className="px-4">
      <div className="flex gap-4 justify-center items-center border-b border-gray-300">
        {/* <div className="border-b"></div> */}
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-2 px-4 border-b-2 transition-all duration-300 ${
              activeTab === index
                ? "border-[#FF0000]"
                : "border-transparent text-gray-500"
            }`}
            onClick={() => setActiveTab(index)}
          >
            {tab}
          </button>
        ))}
      </div>
      <div className="">
        {activeTab === 0 && (
          <div className="overflow-y-auto max-h-[550px] flex flex-col gap-[8px] w-full scrollbar-hide">
            {listMessage.map((message, index) => (
              <div
                className="p-4 flex justify-between items-center"
                key={index}
              >
                <div className="flex items-center">
                  <Image
                    src={message.image}
                    width={40}
                    height={40}
                    alt="mc"
                    className="object-cover "
                  />
                  <div className="ml-3 gap-[8px]">
                    <p className="text-lg font-semibold">{message.name}</p>
                    <p className="text-[#595959]">{message.message}</p>
                  </div>
                </div>
                <div className="flex flex-col gap-3 items-end">
                  <p className="text-[#595959] text-sm">{message.time}</p>
                  {message.unread && (
                    <span className="rounded-full text-white bg-[#3F89BF] w-5 h-[18px] flex items-center justify-center text-xs">
                      {message.unread}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        {activeTab === 1 && <p>Nội dung tab 2</p>}
        {activeTab === 2 && <p>Nội dung tab 3</p>}
      </div>
    </div>
  );
}
