#!/bin/sh

# Wrapper script for deployment with comprehensive error handling
# This script ensures webhooks are always sent, even if deployment fails

set +e  # Don't exit on error

# Function to send webhook notification
send_webhook() {
  local message="$1"
  local url="$2"
  echo "Sending webhook: $message"
  
  # Try to send webhook with timeout and retry
  for i in 1 2 3; do
    echo "Webhook attempt $i..."
    if curl --location 'https://n8n.sanphamweb.com/webhook/c4725e5a-af9a-47b3-b175-12db1484e4c9' \
      --header 'Content-Type: application/json' \
      --data "{\"project_name\": \"Main\", \"url\": \"$url\", \"message\": \"$message\"}" \
      --silent --show-error --max-time 30 --connect-timeout 10; then
      echo "Webhook sent successfully (attempt $i)"
      return 0
    else
      echo "Webhook attempt $i failed, retrying..."
      sleep 2
    fi
  done
  echo "All webhook attempts failed"
  return 1
}

# Function to clean error message - improved to handle control characters
clean_error_message() {
  local error_msg="$1"
  echo "$error_msg" | \
    tr '\n' ' ' | \
    tr '\r' ' ' | \
    tr '\t' ' ' | \
    sed 's/[[:cntrl:]]//g' | \
    sed 's/"/\\"/g' | \
    sed 's/\\/\\\\/g' | \
    sed 's/  */ /g' | \
    sed 's/^ *//' | \
    sed 's/ *$//' | \
    head -c 500
}

# Main deployment function
deploy_with_error_handling() {
  local script_name="$1"
  local webhook_url="$2"
  local success_msg="$3"
  local error_prefix="$4"
  
  echo "Starting deployment with script: $script_name"
  
  # Execute the deployment script and capture all output
  DEPLOY_OUTPUT=$(./"$script_name" 2>&1)
  DEPLOY_EXIT_CODE=$?
  
  echo "Deployment script exit code: $DEPLOY_EXIT_CODE"
  echo "Deployment output: $DEPLOY_OUTPUT"
  
  if [ $DEPLOY_EXIT_CODE -eq 0 ]; then
    echo "Deployment successful!"
    send_webhook "$success_msg" "$webhook_url"
    return 0
  else
    echo "Deployment failed!"
    # Clean and send error message
    ERROR_MSG=$(clean_error_message "$DEPLOY_OUTPUT")
    echo "Cleaned error message: $ERROR_MSG"
    send_webhook "$error_prefix$ERROR_MSG" "$webhook_url"
    return 1
  fi
}

# Check which script to run based on environment or arguments
if [ "$1" = "develop" ] || [ "$DRONE_BRANCH" = "develop" ]; then
  echo "Running develop deployment..."
  deploy_with_error_handling \
    "deploy-develop.sh" \
    "https://staging.giaimasohoc.org/" \
    "✅ Develop đã deploy thành công!" \
    "❌ Develop deploy thất bại! Lỗi: "
else
  echo "Running production deployment..."
  deploy_with_error_handling \
    "deploy.sh" \
    "https://giaimasohoc.org/" \
    "✅ Production đã deploy thành công!" \
    "❌ Production deploy thất bại! Lỗi: "
fi

# Exit with the deployment result
exit $? 