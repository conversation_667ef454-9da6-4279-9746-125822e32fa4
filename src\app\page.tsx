"use client";

import ChatRoom from "@/components/chat-room";
import Footer from "@/layouts/FooterLayout";
import ForumDiscussion from "@/components/forum-discussion";
import Header from "@/layouts/HeaderLayout";
import Image from "next/image";
import Statistical from "@/components/statistical";

const mcList = [
  { name: "<PERSON>ng <PERSON>", image: "/assets/mc-image.png", live: true },
  { name: "MC <PERSON>", image: "/assets/mc-image.png", live: false },
  { name: "MC <PERSON>", image: "/assets/mc-image.png", live: false },
  { name: "<PERSON>", image: "/assets/mc-image.png", live: false },
  { name: "<PERSON>", image: "/assets/mc-image.png", live: true },
  { name: "<PERSON>", image: "/assets/mc-image.png", live: false },
];

const categorys = [
  { name: "<PERSON><PERSON>", image: "/assets/lo-khung.png" },
  { name: "<PERSON><PERSON>", image: "/assets/de-ra.png" },
  { name: "<PERSON><PERSON>", image: "/assets/de-bat-tu.png" },
  { name: "B<PERSON>c Nhớ", image: "/assets/bac-nho.png" },
];

const forums = [
  { image: "/assets/huyen-thoai.png", alt: "huyen-thoai" },
  { image: "/assets/top-1.png", alt: "top-1" },
  { image: "/assets/admin.png", alt: "admin" },
  { image: "/assets/tap-doan.png", alt: "tap-doan" },
  { image: "/assets/hai-ngay.png", alt: "hai-ngay" },
  { image: "/assets/dan-de.png", alt: "dan-de" },
];

const lists = [
  {
    image: "/assets/jackpot.png",
    title: "Số nóng miền bắc",
  },
  {
    image: "/assets/note.png",
    title: "Số nóng miền bắc",
  },
  {
    image: "/assets/note.png",
    title: "Số nóng miền bắc",
  },
  {
    image: "/assets/chat.png",
    title: "Số nóng miền bắc",
  },
  {
    image: "/assets/world.png",
    title: "Số nóng miền bắc",
  },
  {
    image: "/assets/ball.png",
    title: "Số nóng miền bắc",
  },
];

export default function Home() {
  return (
    <>
      <Header />
      <main className="w-full py-10 ">
        <div className="lg:px-20">
          <div className="flex gap-2.5 py-2 px-4 lg:px-10">
            <Image
              src="/assets/ri_live-fill.png"
              width={32}
              height={32}
              alt="live"
              className="object-contain"
            />
            <h1 className="text-[#FF0000] lg:text-2xl text-xl font-semibold">
              Live Xổ Số
            </h1>
          </div>
          <div className="flex overflow-x-auto gap-3 sm:flex-wrap sm:gap-5 items-center no-scrollbar px-4 lg:px-10">
            {mcList.map((mc, index) => (
              <div
                key={index}
                className="flex-shrink-0 flex gap-2.5 items-center border rounded-full py-[6px] pl-2 pr-4 h-11"
              >
                <Image src={mc.image} width={32} height={32} alt="mc" />
                <p className="text-sm flex flex-col">
                  {mc.name}
                  {mc.live && (
                    <span className="text-xs text-[#FF0000] -mt-1">
                      Đang live
                    </span>
                  )}
                </p>
              </div>
            ))}
          </div>
          <div className="flex flex-col lg:flex-row gap-5 mt-6 px-4 lg:px-10">
            <div className="flex flex-col gap-3 w-full lg:w-[895px]">
              <Image
                src={"/assets/frame-10.png"}
                width={895}
                height={465}
                alt="live-screen"
                className="w-full h-auto"
              />
              <p className="text-lg lg:text-xl">
                Soi Cầu Miền Bắc, Dự Đoán XSMB 13/7, Soi Cầu MB, Soi Cầu Đề, Soi
                Cầu Lô, Soi Cầu XSMB, XS247
              </p>
              <div className="flex flex-wrap gap-2.5 items-center py-[6px]">
                <Image
                  src="/assets/mc-image.png"
                  width={32}
                  height={32}
                  alt="mc"
                />
                <p className="text-sm">MC Thăng Long</p>
                <div className="flex items-center gap-1">
                  <Image
                    src={"/assets/Ellipse-2.png"}
                    width={8}
                    height={8}
                    alt="live"
                  />
                  <p className="text-xs text-[#FF0000]">Đang live</p>
                </div>
              </div>
            </div>
            <div className="border rounded-md border-[#E8E8E8] flex flex-col w-full lg:w-[400px]">
              <div className="py-3 px-4 flex justify-between items-center bg-[#F9F9F9]">
                <p>Live Chat</p>
                <Image
                  src="/assets/Vector.png"
                  width={12.5}
                  height={12.5}
                  alt="live"
                />
              </div>
              <div className="py-3 px-4 mt-auto bg-[#F9F9F9] flex justify-center items-center">
                <div className="w-full relative">
                  <input
                    type="text"
                    placeholder="Nhập tin nhắn"
                    className="w-full h-10 rounded-md bg-white px-3 border border-[#E8E8E8]"
                  />
                  <button
                    title="send message"
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    <Image
                      src="/assets/PaperPlaneTilt.png"
                      width={16}
                      height={16}
                      alt="send"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="bg-[#F2F2F2] px-4 py-10 lg:py-20 lg:mt-10 mt-5 lg:px-30">
          <h1 className="lg:text-2xl text-xl font-semibold">
            Video Soi Cầu Xổ Số
          </h1>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mt-5">
            {categorys.map((category, index) => (
              <div
                className="group h-[218px] rounded-sm relative overflow-hidden"
                key={index}
              >
                <div className="absolute inset-0 z-0 transition-transform duration-300 group-hover:scale-105">
                  <Image
                    src={category.image}
                    fill
                    alt=""
                    className="object-cover"
                  />
                </div>
                <div className="absolute inset-0 bg-black/60 z-10" />
                <div className="absolute bottom-5 left-7 z-20 text-white space-y-2">
                  <p className="text-lg font-semibold">{category.name}</p>
                  <button className="bg-white text-black text-xs px-3 py-2 rounded-sm hover:bg-gray-200 transition font-semibold">
                    Xem ngay
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="px-4 py-10 lg:py-20 lg:px-30">
          <h1 className="lg:text-2xl text-xl font-semibold">
            Diễn Đàn Giải Mã Số Học
          </h1>
          <div className="grid grid-cols-2 lg:grid-cols-3 gap-5 mt-5">
            {forums.map((forum, index) => (
              <div
                key={index}
                className="lg:h-[240px] h-[120px] w-full relative rounded-sm overflow-hidden border border-[#A6A6A6] bg-gray-50"
              >
                <Image
                  src={forum.image}
                  fill
                  alt={forum.alt}
                  className="object-contain scale-70"
                />
              </div>
            ))}
          </div>
        </div>
        <ChatRoom />
        <Statistical />
        <div className="px-4 py-10 lg:py-20 lg:px-30 bg-[url('/assets/bg.jpg')] w-full sm:block hidden">
          <div className="grid lg:grid-cols-3 gap-5 items-center">
            {lists.map((list, index) => (
              <div
                className="flex items-center justify-center gap-4 px-10 py-8 bg-white rounded-md overflow-hidden"
                key={index}
              >
                <Image src={list.image} width={40} height={40} alt="list" />
                <p className="text-lg font-semibold capitalize">{list.title}</p>
              </div>
            ))}
          </div>
        </div>
        <ForumDiscussion />
      </main>
      <Footer />
    </>
  );
}
