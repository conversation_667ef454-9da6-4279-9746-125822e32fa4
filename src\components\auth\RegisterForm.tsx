"use client";

import { <PERSON>, EyeOff } from "lucide-react";
import { RegisterFormValues, registerSchema } from "@/schemas/auth.schema";

import toast from "react-hot-toast";
import { useForm } from "react-hook-form";
import { useRegister } from "@/composables/useAuth";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";

interface RegisterFormProps {
  onClose: () => void;
}

export default function RegisterForm({ onClose }: RegisterFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [registrationType, setRegistrationType] = useState<"email" | "phone">(
    "email"
  );
  const registerMutation = useRegister();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data: RegisterFormValues) => {
    try {
      await registerMutation.mutateAsync(data);
      toast.success("Đăng ký thành công!");
      onClose();
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "Đăng ký thất bại!");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="flex gap-2 mb-4">
        <button
          type="button"
          onClick={() => setRegistrationType("email")}
          className={`px-3 py-1 rounded text-sm ${
            registrationType === "email"
              ? "bg-[#FF0000] text-white"
              : "bg-gray-200 text-gray-700"
          }`}
        >
          Email
        </button>
        <button
          type="button"
          onClick={() => setRegistrationType("phone")}
          className={`px-3 py-1 rounded text-sm ${
            registrationType === "phone"
              ? "bg-[#FF0000] text-white"
              : "bg-gray-200 text-gray-700"
          }`}
        >
          Số điện thoại
        </button>
      </div>

      {registrationType === "email" ? (
        <div className="flex flex-col gap-2">
          <label htmlFor="email" className="font-semibold">
            Email
          </label>
          <input
            {...register("email")}
            type="email"
            id="email"
            className="border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF0000]"
            placeholder="Nhập email"
          />
          {errors.email && (
            <span className="text-red-500 text-sm">{errors.email.message}</span>
          )}
        </div>
      ) : (
        <div className="flex flex-col gap-2">
          <label htmlFor="phone" className="font-semibold">
            Số điện thoại
          </label>
          <input
            {...register("phone")}
            type="tel"
            id="phone"
            className="border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF0000]"
            placeholder="Nhập số điện thoại"
          />
          {errors.phone && (
            <span className="text-red-500 text-sm">{errors.phone.message}</span>
          )}
        </div>
      )}

      <div className="flex flex-col gap-2">
        <label className="block font-semibold">Mật khẩu</label>
        <div className="relative">
          <input
            {...register("password")}
            placeholder="********"
            id="password"
            type={showPassword ? "text" : "password"}
            className="w-full border rounded-sm px-3 py-2 text-sm pr-10 focus:outline-none focus:ring-2 focus:ring-[#FF0000]"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
        {errors.password && (
          <span className="text-red-500 text-sm">
            {errors.password.message}
          </span>
        )}
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-[#FF0000] text-white px-2 py-[6px] rounded-lg text-sm disabled:opacity-50"
      >
        {isSubmitting ? "Đang đăng ký..." : "Đăng ký"}
      </button>
    </form>
  );
}
