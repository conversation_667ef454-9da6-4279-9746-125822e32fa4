// src/store/auth.store.ts

import { AuthResponse } from "@/interfaces/auth";
import { create } from "zustand";
import { persist } from "zustand/middleware";

type AuthState = {
  user: AuthResponse["user"] | null;
  token: string | null;
  isAuthenticated: boolean;
  setAuth: (data: AuthResponse) => void;
  logout: () => void;
  initializeAuth: () => void;
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      setAuth: (data) => {
        if (typeof window !== "undefined") {
          localStorage.setItem("token", data.token);
        }
        set({ user: data.user, token: data.token, isAuthenticated: true });
      },
      logout: () => {
        if (typeof window !== "undefined") {
          localStorage.removeItem("token");
        }
        set({ user: null, token: null, isAuthenticated: false });
      },
      initializeAuth: () => {
        if (typeof window !== "undefined") {
          const token = localStorage.getItem("token");
          if (token) {
            set({ token, isAuthenticated: true });
          }
        }
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      }),
    }
  )
);
