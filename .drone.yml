kind: pipeline
name: deploy-production

steps:
  - name: Deploy via SSH (production)
    image: alpine:latest
    environment:
      SSH_KEY:
        from_secret: SSH_KEY
    commands:
      - apk add --no-cache openssh curl jq
      - echo "$SSH_KEY" | base64 -d > /tmp/id_rsa
      - chmod 600 /tmp/id_rsa
      - chmod +x deploy.sh deploy-wrapper.sh
      - ./deploy-wrapper.sh

trigger:
  branch:
    - production

---

kind: pipeline
name: deploy-develop

steps:
  - name: Deploy via SSH (develop-v03)
    image: alpine:latest
    environment:
      SSH_KEY:
        from_secret: SSH_KEY
    commands:
      - apk add --no-cache openssh curl jq
      - echo "$SSH_KEY" | base64 -d > /tmp/id_rsa
      - chmod 600 /tmp/id_rsa
      - chmod +x deploy-develop.sh deploy-wrapper.sh
      - ./deploy-wrapper.sh develop

trigger:
  branch:
    - develop
