#!/bin/sh

# Deploy script for Develop branch
# Disable exit on error to capture error messages
set +e

echo "Starting deployment..."

# Execute deployment and capture output
echo "Executing SSH deployment..."
SSH_OUTPUT=$(ssh -i /tmp/id_rsa -o StrictHostKeyChecking=no root@************* '
  echo "nameserver *******" | sudo tee /etc/resolv.conf > /dev/null
  
  # Load environment variables
  source ~/.bashrc
  source ~/.profile
  
  # Check Node.js installation
  echo "Checking Node.js installation..."
  if ! command -v node &> /dev/null; then
    echo "Node.js not found, installing..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    apt-get install -y nodejs
  fi
  
  # Check npm installation
  echo "Checking npm installation..."
  if ! command -v npm &> /dev/null; then
    echo "npm not found, installing..."
    apt-get install -y npm
  fi
  
  # Check and install pnpm if not available
  echo "Checking pnpm installation..."
  if ! command -v pnpm &> /dev/null; then
    echo "Installing pnpm..."
    npm install -g pnpm
  fi
  
  # Check and install pm2 if not available
  echo "Checking pm2 installation..."
  if ! command -v pm2 &> /dev/null; then
    echo "Installing pm2..."
    npm install -g pm2
  fi
  
  # Verify tools are available with full paths
  echo "Checking tools availability..."
  echo "Node.js version: $(node --version)"
  echo "npm version: $(npm --version)"
  echo "pnpm version: $(pnpm --version)"
  echo "pm2 version: $(pm2 --version)"
  
  # Proceed with deployment
  cd /var/www/html/ticket_vod_nextjs
  git pull --rebase
  pnpm install
  pnpm build
  pm2 restart ticket-vod-nextjs
' 2>&1)

SSH_EXIT_CODE=$?
echo "SSH Exit Code: $SSH_EXIT_CODE"
echo "SSH Output: $SSH_OUTPUT"

# Exit with the SSH exit code (wrapper script will handle webhook)
exit $SSH_EXIT_CODE 