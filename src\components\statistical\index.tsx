"use client";

import { Crown, Trophy } from "lucide-react";

import Image from "next/image";

export default function Statistical() {
  return (
    <div className="px-4 py-10 mt-5 lg:px-30 gap-8 lg:py-20 hidden lg:block">
      <h1 className="md:text-2xl text-xl font-semibold">Th<PERSON><PERSON></h1>
      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mt-5">
          {/* table 1 */}
          <div className="flex flex-col items-center [&_div]:w-full ">
            <div className="flex items-center justify-center gap-2 text-[#FF0000] border border-[#A6A6A6] py-4 px-2.5 rounded-t-sm overflow-hidden w-full">
              <Crown fill="#FF0000" />
              <p className="uppercase font-semibold">Cao thủ huyền thoại</p>
            </div>
            <div className="w-full border-l border-r border-b border-[#A6A6A6] rounded-b-sm">
              <table className="w-full border-collapse table-auto">
                <thead>
                  <tr>
                    <th className="border-b border-r border-[#A6A6A6] bg-[#FFD133] p-2.5 w-[28%] text-center">
                      Cao Thủ
                    </th>
                    <th className="border-b border-r border-[#A6A6A6] bg-[#FF0000] p-2.5 w-[12%] text-white text-center">
                      Tổng
                    </th>
                    {[8, 7, 6, 5, 4, 3, 2, 1].map((num, index) => (
                      <th
                        key={num}
                        className={`border-b border-[#A6A6A6] p-2.5 w-[7.5%] text-center ${
                          index < 7 ? "border-r" : ""
                        }`}
                      >
                        {num}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {Array(6)
                    .fill(0)
                    .map((_, idx) => (
                      <tr key={idx}>
                        <td
                          className={`border-r border-[#A6A6A6] text-center p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        >
                          <div className="flex items-center justify-center gap-2">
                            <Image
                              src="/assets/mc-image.png"
                              alt="avatar"
                              width={24}
                              height={24}
                            />
                            <p className="text-[#595959] text-sm">PHONGPRO68</p>
                          </div>
                        </td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center p-2.5 font-semibold bg-[#FFE6E6] ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        >
                          2
                        </td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center bg-yellow-100 font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        >
                          2
                        </td>
                        <td
                          className={`text-center bg-yellow-100 font-semibold p-2.5  ${
                            idx < 5 ? "border-b border-[#A6A6A6]" : ""
                          }`}
                        >
                          2
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* table 2 */}
          <div className="flex flex-col items-center [&_div]:w-full">
            <div className="flex items-center justify-center gap-2 text-[#FF0000] border border-[#A6A6A6] py-4 px-2.5 rounded-t-sm overflow-hidden w-full">
              <Trophy fill="#FF0000" />
              <p className="uppercase font-semibold">Cao thủ top 1</p>
            </div>
            <div className="w-full border-l border-r border-b border-[#A6A6A6] rounded-b-sm">
              <table className="w-full border-collapse table-auto">
                <thead>
                  <tr>
                    <th className="border-b border-r border-[#A6A6A6] bg-[#FFD133] p-2.5 w-[28%] text-center">
                      Cao Thủ
                    </th>
                    <th className="border-b border-r border-[#A6A6A6] bg-[#FF0000] p-2.5 w-[12%] text-white text-center">
                      Tổng
                    </th>
                    {[8, 7, 6, 5, 4, 3, 2, 1].map((num, index) => (
                      <th
                        key={num}
                        className={`border-b border-[#A6A6A6] p-2.5 w-[7.5%] text-center ${
                          index < 7 ? "border-r" : ""
                        }`}
                      >
                        {num}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {Array(6)
                    .fill(0)
                    .map((_, idx) => (
                      <tr key={idx}>
                        <td
                          className={`border-r border-[#A6A6A6] text-center p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        >
                          <div className="flex items-center justify-center gap-2">
                            <Image
                              src="/assets/mc-image.png"
                              alt="avatar"
                              width={24}
                              height={24}
                            />
                            <p className="text-[#595959] text-sm">PHONGPRO68</p>
                          </div>
                        </td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center p-2.5 font-semibold bg-[#FFE6E6] ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        >
                          2
                        </td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        ></td>
                        <td
                          className={`border-r border-[#A6A6A6] text-center bg-yellow-100 font-semibold p-2.5 ${
                            idx < 5 ? "border-b" : ""
                          }`}
                        >
                          2
                        </td>
                        <td
                          className={`text-center bg-yellow-100 font-semibold p-2.5 ${
                            idx < 5 ? "border-b border-[#A6A6A6]" : ""
                          }`}
                        >
                          2
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
