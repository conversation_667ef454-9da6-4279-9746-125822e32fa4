import { AuthResponse, LoginRequest, RegisterRequest } from "@/interfaces/auth";

import { BASE_URL } from './../helpers/apiConfig';

export const authService = {
  register: async (payload: RegisterRequest): Promise<AuthResponse> => {
    const res = await BASE_URL.post<AuthResponse>("/register", payload);
    return res.data;
  },
  login: async (payload: LoginRequest): Promise<AuthResponse> => {
    const res = await BASE_URL.post<AuthResponse>("/login", payload);
    return res.data;
  },
};
