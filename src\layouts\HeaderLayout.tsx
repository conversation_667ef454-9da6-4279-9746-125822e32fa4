"use client";

import { LogOut, MenuIcon, User } from "lucide-react";
import { useEffect, useState } from "react";

import { AuthModal } from "@/components/auth";
import Image from "next/image";
import NavBottom from "./NavBottom";
import Navbar from "./NavbarLayout";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/auth.store";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isShowModal, setIsShowModal] = useState(false);
  const [authMode, setAuthMode] = useState<"login" | "register">("login");
  const [isMounted, setIsMounted] = useState(false);

  const { user, isAuthenticated, logout, initializeAuth } = useAuthStore();

  useEffect(() => {
    setIsMounted(true);
    initializeAuth();
  }, [initializeAuth]);

  const handleLogout = () => {
    logout();
    toast.success("Đăng xuất thành công!");
  };

  const openAuthModal = (mode: "login" | "register") => {
    setAuthMode(mode);
    setIsShowModal(true);
  };

  return (
    <>
      <header className="w-full bg-[#FF0000] py-3 px-4 md:px-30 flex items-center justify-between">
        <div className="flex gap-2 items-center">
          <Image src="/assets/logo.png" width={40} height={40} alt="logo" />
          <p className="text-xl text-white whitespace-nowrap hidden lg:block">
            GiaiMaSoHoc
          </p>
        </div>
        <div className="hidden md:block">
          <Navbar />
        </div>
        <div className="flex items-center gap-2 md:gap-4">
          {isAuthenticated ? (
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2 text-white">
                <User size={16} />
                <span className="text-sm hidden sm:block">
                  {user?.email || user?.phone || "User"}
                </span>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center gap-1 text-white hover:text-gray-200 text-sm"
                title="Đăng xuất"
              >
                <LogOut size={16} />
                <span className="hidden sm:block">Đăng xuất</span>
              </button>
            </div>
          ) : (
            <>
              <button
                className=" text-[#FF0000] text-sm lg:text-md bg-white rounded-sm px-3 py-1.5 font-semibold"
                onClick={() => openAuthModal("login")}
              >
                Đăng nhập
              </button>
              <button
                className=" text-sm lg:text-md bg-white rounded-sm px-3 py-1.5 font-semibold"
                onClick={() => openAuthModal("register")}
              >
                Đăng ký
              </button>
            </>
          )}
          <button
            title="Menu"
            className="block md:hidden text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <MenuIcon />
          </button>
        </div>
      </header>

      {isMenuOpen && (
        <div className="md:hidden transition-all duration-200 bg-white px-4 py-3 shadow-sm">
          <Navbar />
          {!isAuthenticated && (
            <div className="grid grid-cols-2 gap-2 mt-2">
              <button
                className="text-[#FF0000] text-sm bg-white rounded-lg px-3 py-1.5 border border-[#FF0000] font-semibold"
                onClick={() => openAuthModal("login")}
              >
                Đăng nhập
              </button>
              <button
                className="text-sm bg-[#FF0000] text-white rounded-lg px-3 py-1.5 font-semibold"
                onClick={() => openAuthModal("register")}
              >
                Đăng ký
              </button>
            </div>
          )}
          {isAuthenticated && (
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User size={16} />
                <span className="text-sm">
                  {user?.email || user?.phone || "User"}
                </span>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center gap-1 text-red-600 text-sm"
              >
                <LogOut size={16} />
                Đăng xuất
              </button>
            </div>
          )}
        </div>
      )}

      {isMounted && isShowModal && (
        <AuthModal
          onClose={() => setIsShowModal(false)}
          initialMode={authMode}
        />
      )}

      <NavBottom />
    </>
  );
}
