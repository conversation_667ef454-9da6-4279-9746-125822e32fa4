"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useLogin, useRegister } from "@/composables/useAuth"; // hooks từ tanstack query

import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MenuIcon } from "lucide-react";
import NavBottom from "./NavBottom";
import Navbar from "./NavbarLayout";
import { toast } from "sonner";
import { useState } from "react";

export default function Header() {
  const [open, setOpen] = useState(false);
  const [loginData, setLoginData] = useState({ username: "", password: "" });
  const [registerData, setRegisterData] = useState({
    email: "",
    phone: "",
    password: "",
  });

  const loginMutation = useLogin();
  const registerMutation = useRegister();

  const handleLogin = () => {
    loginMutation.mutate(loginData);
    if (loginMutation) {
      toast.success("Đăng nhập thành công");
      setOpen(false);
    } else {
      toast.error("Đăng nhập thất bại");
    }
  };

  const handleRegister = () => {
    registerMutation.mutate(registerData);
  };

  return (
    <>
      <header className="w-full bg-[#FF0000] py-3 px-4 md:px-30 flex items-center justify-between">
        <div className="flex gap-2 items-center">
          <Image src="/assets/logo.png" width={40} height={40} alt="logo" />
          <p className="text-xl text-white whitespace-nowrap hidden lg:block">
            GiaiMaSoHoc
          </p>
        </div>
        <div className="hidden md:block">
          <Navbar />
        </div>
        <div className="flex items-center gap-2 md:gap-4">
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <button className="text-[#FF0000] text-xs bg-white rounded-lg px-3 py-1.5 border border-[#FF0000] font-semibold">
                Đăng nhập
              </button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader className="mb-6">
                <DialogTitle>Đăng nhập</DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              <div className="grid gap-4">
                <div className="grid gap-3">
                  <Label htmlFor="username">Email hoặc SĐT</Label>
                  <Input
                    id="username"
                    name="username"
                    value={loginData.username}
                    onChange={(e) =>
                      setLoginData({ ...loginData, username: e.target.value })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="password">Mật khẩu</Label>
                  <Input
                    id="password"
                    type="password"
                    name="password"
                    value={loginData.password}
                    onChange={(e) =>
                      setLoginData({ ...loginData, password: e.target.value })
                    }
                  />
                </div>
              </div>
              <div className="text-gray-800 text-sm">
                <p>Lấy lại mật khẩu liên hệ Facebook</p>
                <span>Admin: http://www.facebook.com/nhom247</span>
              </div>
              <div className="flex items-center gap-x-2 mb-3">
                <Input type="checkbox" id="remember" className="h-4 w-4" />
                <Label htmlFor="remember" className="font-semibold">
                  Duy trì đăng nhập
                </Label>
              </div>
              <Button
                variant="destructive"
                onClick={handleLogin}
                disabled={loginMutation.isPending}
              >
                {loginMutation.isPending ? "Đang đăng nhập..." : "Đăng nhập"}
              </Button>
            </DialogContent>
          </Dialog>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <button className=" text-xs bg-white rounded-lg px-3 py-1.5 font-semibold">
                Đăng ký
              </button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader className="mb-6">
                <DialogTitle>Đăng ký</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4">
                <div className="grid gap-3">
                  <Label htmlFor="email">Email (tuỳ chọn)</Label>
                  <Input
                    id="email"
                    name="email"
                    value={registerData.email}
                    onChange={(e) =>
                      setRegisterData({
                        ...registerData,
                        email: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="phone">Số điện thoại (tuỳ chọn)</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={registerData.phone}
                    onChange={(e) =>
                      setRegisterData({
                        ...registerData,
                        phone: e.target.value,
                      })
                    }
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="password">Mật khẩu</Label>
                  <Input
                    id="password"
                    type="password"
                    name="password"
                    value={registerData.password}
                    onChange={(e) =>
                      setRegisterData({
                        ...registerData,
                        password: e.target.value,
                      })
                    }
                  />
                </div>
              </div>
              <Button
                variant="destructive"
                onClick={handleRegister}
                disabled={registerMutation.isPending}
              >
                {registerMutation.isPending
                  ? "Đang đăng ký..."
                  : "Đăng ký ngay"}
              </Button>
            </DialogContent>
          </Dialog>

          <button title="Menu" className="block md:hidden text-white">
            <MenuIcon />
          </button>
        </div>
      </header>

      <NavBottom />
    </>
  );
}
