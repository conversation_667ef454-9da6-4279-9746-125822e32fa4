"use client";

import Image from "next/image";
import Login from "@/components/login-modal";
import { MenuIcon } from "lucide-react";
import NavBottom from "./NavBottom";
import Navbar from "./NavbarLayout";
import { useState } from "react";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isShowModal, setIsShowModal] = useState(false);

  return (
    <>
      <header className="w-full bg-[#FF0000] py-3 px-4 md:px-30 flex items-center justify-between">
        <div className="flex gap-2 items-center">
          <Image src="/assets/logo.png" width={40} height={40} alt="logo" />
          <p className="text-xl text-white whitespace-nowrap">GiaiMaSoHoc</p>
        </div>
        <div className="hidden md:block">
          <Navbar />
        </div>
        <div className="flex items-center gap-2 md:gap-4">
          <button
            className="hidden sm:block text-[#FF0000] text-sm bg-white rounded-lg px-3 py-1.5 font-semibold"
            onClick={() => setIsShowModal(true)}
          >
            Đăng nhập
          </button>
          <button className="hidden sm:block text-sm bg-white rounded-lg px-3 py-1.5 font-semibold">
            Đăng ký
          </button>

          <button
            title="Menu"
            className="block md:hidden text-white"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <MenuIcon />
          </button>
        </div>
      </header>
      {isMenuOpen && (
        <div className="md:hidden transition-all duration-200 bg-white px-4 py-3 shadow-sm">
          <Navbar />
          <div className="grid grid-cols-2 gap-2 mt-2">
            <button className="text-[#FF0000] text-sm bg-white rounded-lg px-3 py-1.5 border border-[#FF0000] font-semibold">
              Đăng nhập
            </button>
            <button className="text-sm bg-[#FF0000] text-white rounded-lg px-3 py-1.5 font-semibold">
              Đăng ký
            </button>
          </div>
        </div>
      )}
      {isShowModal && <Login />}
      <NavBottom />
    </>
  );
}
