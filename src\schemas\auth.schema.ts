import { z } from "zod";

const phoneRegex = /^0\d{9}$/;

export const registerSchema = z.object({
  email: z.string().email("<PERSON>ail không hợp lệ").optional(),
  phone: z
    .string()
    .regex(phoneRegex, "Số điện thoại không hợp lệ")
    .optional(),
  password: z.string().min(6, "Mật khẩu tối thiểu 6 ký tự"),
}).refine(
  (data) => data.email || data.phone,
  { message: "<PERSON>ui lòng nhập email hoặc số điện thoại", path: ["email"] }
);

export const loginSchema = z.object({
  username: z
    .string()
    .min(1, "<PERSON>ui lòng nhập email hoặc số điện thoại")
    .refine(
      (val) => /\S+@\S+\.\S+/.test(val) || phoneRegex.test(val),
      "Email hoặc số điện thoạ<PERSON> không hợp lệ"
    ),
  password: z.string().min(6, "<PERSON><PERSON><PERSON> khẩu tối thiểu 6 ký tự"),
});

export type RegisterFormValues = z.infer<typeof registerSchema>;
export type LoginFormValues = z.infer<typeof loginSchema>;
