"use client";

import {
  Ellip<PERSON>Vert<PERSON>,
  Paperclip,
  Pin,
  Send,
  UsersRound,
} from "lucide-react";

import Image from "next/image";
import Tabs from "../tabs";

export default function ChatRoom() {
  return (
    <div className="bg-[url('/assets/bg-gradient.jpg')] bg-cover w-full px-4 py-10 lg:py-20 lg:mt-10 mt-5 lg:px-30 gap-8">
      <h1 className="lg:text-2xl text-xl font-semibold">Chat Room</h1>
      <div className="border border-[#E8E8E8] overflow-y-hidden rounded-sm lg:flex lg:flex-nowrap bg-white mt-5">
        <div className="lg:flex lg:flex-col lg:border-r border-[#E8E8E8] lg:w-[340px]">
          <div className="pt-5 px-8 space-y-2 flex flex-col items-center justify-center mb-4">
            <input
              type="text"
              placeholder="Danh sách nhóm"
              className="px-5 py-2 bg-[#E8E8E8] rounded-xl overflow-hidden lg:min-w-[280px] min-w-[320px]"
            />
          </div>
          <Tabs />
        </div>
        <div className="lg:flex-1 lg:flex-col lg:flex hidden">
          <div className="p-6 bg-white flex flex-row items-center justify-between border-b">
            <div className="flex flex-row items-center gap-4">
              <Image
                src="/assets/mc-image.png"
                width={40}
                height={40}
                alt="mc"
                className="object-cover"
              />
              <div className="flex items-center">
                <div>
                  <p className="lg:text-2xl text-xl">Tán Gẫu</p>
                  <div className="flex items-center justify-start gap-2">
                    <span className="rounded-full bg-[#68D391] size-[10px]"></span>
                    <p className="text-xs text-[#595959]">Online</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-6 items-center">
              <div className="flex items-center gap-2">
                <Pin size={24} />
                <p className="text-[#595959]">12</p>
              </div>
              <div className="flex items-center gap-2">
                <UsersRound size={24} />
                <p className="text-[#595959]">44</p>
              </div>
              <div>
                <EllipsisVertical />
              </div>
            </div>
          </div>
          <div className="bg-[#E8E8E8] border border-[#E8E8E8] p-8 flex-1"></div>
          <div className="mt-auto p-8 gap-8 flex items-center">
            <Paperclip size={24} />
            <div className="min-w-[280px] flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Nhập tin nhắn"
                  className="border rounded-lg px-[14px] py-2.5 w-full border-[#A6A6A] shadow-sm"
                />
                <button
                  title="Gửi"
                  type="submit"
                  className="absolute right-0 top-0 bottom-0 px-5 z-10 "
                >
                  <Send size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
