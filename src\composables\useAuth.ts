import { authService } from "@/services/auth.service";
import { useAuthStore } from "@/stores/auth.store";
import { useMutation } from "@tanstack/react-query";

export const useLogin = () => {
  const setAuth = useAuthStore((state) => state.setAuth);
  return useMutation({
    mutationFn: authService.login,
    onSuccess: (res) => {
      setAuth(res);
      localStorage.setItem("token", res.token);
    },
  });
};

export const useRegister = () => {
  const setAuth = useAuthStore((state) => state.setAuth);
  return useMutation({
    mutationFn: authService.register,
    onSuccess: (res) => {
      setAuth(res);
      localStorage.setItem("token", res.token);
    },
  });
};
