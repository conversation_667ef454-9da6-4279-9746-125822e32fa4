import { authService } from "@/services/auth.service";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/auth.store";
import { useMutation } from "@tanstack/react-query";

export const useLogin = () => {
  const setAuth = useAuthStore((state) => state.setAuth);
  return useMutation({
    mutationFn: authService.login,
    onSuccess: (res) => {
      setAuth(res);
      if (typeof window !== "undefined") {
        localStorage.setItem("token", res.token);
      }
      toast.success("Đăng nhập thành công!");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Đăng nhập thất bại!");
    },
  });
};

export const useRegister = () => {
  const setAuth = useAuthStore((state) => state.setAuth);
  return useMutation({
    mutationFn: authService.register,
    onSuccess: (res) => {
      setAuth(res);
      if (typeof window !== "undefined") {
        localStorage.setItem("token", res.token);
      }
      toast.success("Đăng ký thành công!");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Đăng ký thất bại!");
    },
  });
};
