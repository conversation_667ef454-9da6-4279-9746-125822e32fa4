"use client";

import { <PERSON>, EyeOff } from "lucide-react";
import { LoginFormValues, loginSchema } from "@/schemas/auth.schema";

import toast from "react-hot-toast";
import { useForm } from "react-hook-form";
import { useLogin } from "@/composables/useAuth";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";

interface LoginFormProps {
  onClose: () => void;
}

export default function LoginForm({ onClose }: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const loginMutation = useLogin();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormValues) => {
    try {
      await loginMutation.mutateAsync(data);
      toast.success("<PERSON><PERSON>ng nhập thành công!");
      onClose();
    } catch (error: any) {
      toast.error(error?.response?.data?.message || "Đăng nhập thất bại!");
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="flex flex-col gap-2">
        <label htmlFor="username" className="font-semibold">
          Email hoặc số điện thoại
        </label>
        <input
          {...login("username")}
          type="text"
          id="username"
          className="border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF0000]"
          placeholder="Nhập email hoặc số điện thoại"
        />
        {errors.username && (
          <span className="text-red-500 text-sm">
            {errors.username.message}
          </span>
        )}
      </div>
      <div className="flex flex-col gap-2">
        <label className="block font-semibold">Mật khẩu</label>
        <div className="relative">
          <input
            {...register("password")}
            placeholder="********"
            id="password"
            type={showPassword ? "text" : "password"}
            className="w-full border rounded-sm px-3 py-2 text-sm pr-10 focus:outline-none focus:ring-2 focus:ring-[#FF0000]"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
        {errors.password && (
          <span className="text-red-500 text-sm">
            {errors.password.message}
          </span>
        )}
      </div>

      <div className="text-gray-800 text-sm py-3">
        <p>Lấy lại mật khẩu điền dàn liên hệ Facebook</p>
        <span>Admin: http://www.facebook.com/nhom247</span>
      </div>

      <div className="flex items-center gap-x-2 mb-3">
        <input type="checkbox" id="remember" className="h-4 w-4" />
        <label htmlFor="remember" className="font-semibold">
          Duy trì đăng nhập
        </label>
      </div>

      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-[#FF0000] text-white px-2 py-[6px] rounded-lg text-sm disabled:opacity-50"
      >
        {isSubmitting ? "Đang đăng nhập..." : "Đăng nhập"}
      </button>
    </form>
  );
}
